# Development Guide for Ghost Automator Pro

This document provides technical information and guidelines for developers working on the `Ghost Automator Pro` project.

## 🛠️ Technology Stack

`Ghost Automator Pro` is built with:

- **BAS custom interface** - for creating a website-based application that runs in Browser Automation Studio
- **Vue 3** - Progressive JavaScript framework for building user interfaces
- **Flowbite Vue** - Vue 3 components built on top of Tailwind CSS and Flowbite
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **TypeScript** - for type-safe code and enhanced developer experience
- **Vite** - Fast build tool and development server
- **Pinia** - State management for Vue 3 applications

## 🚀 Development Setup

### Prerequisites

- Windows operating system
- Node.js and npm installed

### Installation

- Clone the repository
- Install dependencies:
  ```bash
  npm install
  ```
- Start the development server:
  ```bash
  npm run dev
  ```

## 📝 Commit Conventions

We follow conventional commits to maintain a clean and meaningful git history. This helps with automated versioning and
changelog generation.

### Commit Message Format

Each commit message consists of a **header**, an optional **body**, and an optional **footer**:

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

#### Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation changes
- **style**: Changes that don't affect code functionality (formatting, etc.)
- **refactor**: Code changes that neither fix bugs nor add features
- **perf**: Performance improvements
- **test**: Adding or correcting tests
- **chore**: Changes to a build process or auxiliary tools

#### Description

The description should be a clear and concise explanation of the change. It can be formatted in two ways:

1. **Single-line description**: A brief summary of the change

   ```
   feat(profile): add browser fingerprint customization
   ```

2. **Multi-line description**: A brief summary followed by a more detailed explanation

   ```
   feat(profile): add browser fingerprint customization

   - Add feature like bla-bla
   - Add feature like bu-bu
   ```

   Or:

   ```
   feat(profile): add browser fingerprint customization

   Add feature making fast to improve d, because we need that.
   ```

#### Examples

**Single-line commit message:**

```
feat(profile): add browser fingerprint customization
```

```
fix(auth): resolve login issue with special characters
```

```
docs(readme): update installation instructions
```

**Multi-line commit message with bullet points:**

```
feat(profile): add browser fingerprint customization

- Added custom user agent selection
- Implemented canvas fingerprint randomization
- Added timezone spoofing capability
```

**Multi-line commit message with paragraph:**

```
fix(auth): resolve login issue with special characters

This fix addresses the authentication failure that occurred when users
attempted to log in with passwords containing special characters.
The input sanitization process has been updated to properly handle these cases.
```

## 🧪 Testing

Details about testing procedures and frameworks will be added as the project evolves.

## 🏗️ Build Process

Instructions for building the application for production will be provided in future updates.

## 🌐 BAS Custom Interface

`Ghost Automator Pro` creates a custom BAS (Browser Automation Studio) interface for browser automation capabilities.

For detailed information about the BAS custom interface, please refer to
the [BAS_CUSTOM_INTERFACE.md](BAS_CUSTOM_INTERFACE.md) file. This separate document provides comprehensive guidance on:

- Overview of BAS custom interface
- Requirements and setup
- API documentation
- Deployment strategies
- Vue.js integration
- And more

The BAS custom interface documentation has been moved to a separate file due to its extensive nature and importance to
the project.

## 🎨 UI Design

For detailed UI design specifications, please refer to the [UI_DESIGN_REQUIREMENTS.md](UI_DESIGN_REQUIREMENTS.md) file. This document outlines:

- Layout structure (header, sidebar, content areas)
- Navigation structure
- Visual design guidelines
- Responsive design considerations
- Component library
- Implementation process

Following these design requirements will ensure a consistent and professional user experience across the application.

## 📄 License

[MIT](http://opensource.org/licenses/MIT)
